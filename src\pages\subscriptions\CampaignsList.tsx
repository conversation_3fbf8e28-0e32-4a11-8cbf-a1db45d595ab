import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON><PERSON>rumb, Modal, Spin, Form, Switch, DatePicker, Divider, Select, Input, Radio, Upload, Button } from "antd";
import { assets } from "../../assets/assets.ts";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { AppDispatch } from "../../features/store.ts";
import { getAbnTypesAll } from "../../features/subscription/abnTypeSlice.ts";
import {
  getSalesPeriodsAll,
  ISalePeriod,
} from "../../features/subscription/salesPeriodsSlice.ts";
import { getLastSubscriptionByClient } from "../../features/subscription/subsSlice.ts";
import dayjs from "dayjs";
import Loading from "../../components/Loading.tsx";
import { toast } from "react-toastify";
import { InfoIcon, SearchIcon, UploadIcon, UserIcon, CreditCardIcon, IdCardIcon, PrinterIcon } from "lucide-react";
import FormItem from "antd/es/form/FormItem/index";
import { getStationsBySubscriptionType, getStationsForTrip } from "../../features/subscription/stationSlice.ts";
import { getDelegationsByGovernorate } from "../../features/subscription/delegationSlice.ts";
import { getLinesByStations, getLineStationsAndRoutes } from "../../features/subscription/lineSlice.ts";
import { LineDisplay, SubsReceipt } from "../../components/index.ts";
import { GetRestDaysData } from "../../data/StaticData.ts";
import { searchCitoyen, searchStudentWithCIN, searchStudentWithoutCIN, storeClientWithSubscription } from "../../features/subscription/subsSlice.ts";
import CropModal from "../../components/subs/CropModal.tsx";
import { getGovernorateAll } from "../../features/subscription/governorateSlice.ts";
import { getPeriodicityAll } from "../../features/subscription/periodicitySlice.ts";
import { getEstablishmentAll } from "../../features/subscription/establishmentSlice.ts";
import { getSchoolDegreeAll, storeSchoolDegree } from "../../features/subscription/schoolDegreeSlice.ts";
import { formatImagePath } from "../../tools/helpers.ts";
import PaymentModal from "../../components/subs/PaymentModal.tsx";
import SubsCardPrintModal from "../../components/subs/SubsCardPrintModal.tsx";

const CampaignsList = () => {
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;
  const [form] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();
  const actionRef = useRef<any>();

  const [loadingFetch, setLoadingFetch] = useState(true);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);

  // subs states
  const [selectedAbnType, setSelectedAbnType] = useState<any>(null);
  const [isStagiaire, setIsStagiaire] = useState(false);
  const [loadingSearchClient, setLoadingSearchClient] = useState(false);
  const [isReceiptModalVisible, setIsReceiptModalVisible] = useState(false);
  const [activePeriod, setActivePeriod] = useState<ISalePeriod | null>(null);

  // Last subscription state
  const lastSubscription = useSelector((state: any) => state.subscription.lastSubscription?.data);
  const lastSubscriptionLoading = useSelector((state: any) => state.subscription.lastSubscriptionLoading);

  const [abnRecord, setAbnRecord] = useState<any>(null)
  const [paymentModal, setPaymentModal] = useState(false);
  const [isRenewal, setIsRenewal] = useState(false);
  const [isCardModalVisible, setIsCardModalVisible] = useState(false);

  const [filteredDelegations, setFilteredDelegations] = useState<any[]>([]);
  const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);

  const [specialClient, setSpecialClient] = useState<any>();

  const [cropModalVisible,setCropModalVisible] = useState(false);
  const [imageSrc, setImageSrc] = useState<string | null>(null);

  const [croppedImage, setCroppedImage] = useState<string | null>(null);

  const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<number | null>(null);
  const [selectedStationDepart, setSelectedStationDepart] = useState(null);
  const [selectedStationArrival, setSelectedStationArrival] = useState(null);
  const [selectedDelegation, setSelectedDelegation] = useState<any>(null);
  const [selectedLine, setSelectedLine] = useState<number | null>(null);
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [selectedEstablishment, setSelectedEstablishment] = useState<any>(null);
  const [maxDaysPerWeek , setMaxDaysPerWeek] = useState<any>(null);
  const [isReversed, setIsReversed] = useState(false);

  const [stations, setStations] = useState<any[]>([]);
  const [isLoadingStations, setIsLoadingStations] = useState(false);

  const [connectedStations, setConnectedStations] = useState<any[]>([]);
  const [isLoadingConnectedStations, setIsLoadingConnectedStations] = useState(false);

  const [availableLines, setAvailableLines] = useState<any[]>([]);
  const [isLoadingLines, setIsLoadingLines] = useState(false);

  const [lineDetails, setLineDetails] = useState<any>(null);
  const [isLoadingLineDetails, setIsLoadingLineDetails] = useState(false);

  const governorates = useSelector((state: any) => state.governorate.items.data);
  const periodicities = useSelector((state: any) => state.periodicity.items.data);
  const establishments = useSelector((state: any) => state.establishment.items.data);
  const schoolDegrees = useSelector((state: any) => state.schoolDegree.items.data);

  const restDaysData:any = GetRestDaysData()

  /* -------------------------------------
  * Fetch all data from store
  -------------------------------------- */
  const fetchStoreData = async () => {
    try {
      setLoadingFetch(true);

      const promises = [];

      if (!subsTypes?.length) {
        promises.push(dispatch(getAbnTypesAll()).unwrap());
      }
      if (!salesPeriods?.length) {
        promises.push(dispatch(getSalesPeriodsAll()).unwrap());
      }

      if(!governorates?.length){
        promises.push(dispatch(getGovernorateAll()).unwrap());
      }

      if (!periodicities?.length) {
          promises.push(dispatch(getPeriodicityAll()).unwrap());
      }

      if (!establishments?.length) {
          promises.push(dispatch(getEstablishmentAll()).unwrap());
      }
      if (!schoolDegrees?.length) {
          promises.push(dispatch(getSchoolDegreeAll()).unwrap());
      }

      // Fetch the last subscription
      promises.push(dispatch(getLastSubscriptionByClient()).unwrap());

      await Promise.all(promises);
    } catch (error) {
      console.error("Error fetching initial data:", error);
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    setLoadingFetch(true);
    fetchStoreData();
  }, []);


  const subsTypes = useSelector((state: any) =>
    state.abnType.items.data?.filter(
      (item: any) => !item.is_impersonal && !item.is_conventional
    )
  );
  const salesPeriods = useSelector(
    (state: any) => state.salesPeriod.items.data
  );

  const getTypeImage = (id: number) => {
    switch (id) {
      case 1:
        return assets.scolaire_img;
      case 2:
        return assets.univ_img;
      default:
        return assets.civil_img;
    }
  };
  type SubscriptionStatus = {
    status: "available" | "upcoming" | "none";
    daysRemaining?: number;
  };
  const getSubscriptionStatus = (
    subscriptionId: number,
    salesPeriods: ISalePeriod[]
  ): SubscriptionStatus => {
    const now = dayjs();

    const relatedPeriods = salesPeriods.filter(
      (period) => period.id_abn_type === subscriptionId
    );

    const activePeriod = relatedPeriods.find((period) => {
      return (
        dayjs(period.date_start).isBefore(now) &&
        dayjs(period.date_end).isAfter(now)
      );
    });

    if (activePeriod) {
      return { status: "available" };
    }

    // Find next upcoming period
    const upcomingPeriods = relatedPeriods
      .filter((period) => dayjs(period.date_start).isAfter(now))
      .sort((a, b) => dayjs(a.date_start).diff(dayjs(b.date_start))); // earliest first

    if (upcomingPeriods.length > 0) {
      const nextPeriod = upcomingPeriods[0];
      const daysRemaining = dayjs(nextPeriod.date_start).diff(now, "day");
      return { status: "upcoming", daysRemaining };
    }

    // If no upcoming period
    return { status: "none" };
  };

  const handleClickCard = (subscription: any) => {
    const toastId = toast.loading(t("messages.loading"), {
      position: 'top-center',
    });
    const subscriptionStatus = getSubscriptionStatus(
      subscription.id,
      salesPeriods || []
    );

    if (subscriptionStatus.status === "available") {
      setSelectedAbnType(subscription);
      toast.dismiss(toastId);
      if (!subscription.is_student) {
        setSpecialClient("CIVIL");
        form.setFieldsValue({ special_client: "CIVIL" });
      } else {
        setSpecialClient(null);
      }

      // Set the active period for the selected subscription type
      const now = dayjs();
      const relatedPeriods = (salesPeriods || []).filter(
        (period: any) => period.id_abn_type === subscription.id
      );
      const active = relatedPeriods.find(
        (period: any) =>
          dayjs(period.date_start).isBefore(now) &&
          dayjs(period.date_end).isAfter(now)
      );
      setActivePeriod(active || null);

      setIsAddModalVisible(true);
    } else if (subscriptionStatus.status === "upcoming") {
        toast.update(toastId, {
            render: t("messages.not_available_sale_period"),
            type: "info",
            isLoading: false,
            autoClose: 3000
        });
    } else {
       toast.update(toastId, {
            render: t("messages.not_available_sale_period"),
            type: "warning",
            isLoading: false,
            autoClose: 3000
        });
    }
  };

  /*-----------------------------------
  * Breadcrumb
  -----------------------------------*/
  const breadcrumbItems = [
    {
      title: (
        <Link className="!bg-white" to="/auth/commercial-dashboard">
          {t("auth_sidebar.dashboard")}
        </Link>
      ),
    },
    {
      title: t("auth_sidebar.manage_subscriptions"),
    },
  ];


  /*|--------------------------------------------------------------------------
  |  - HANDLE ACTIONS
  |-------------------------------------------------------------------------- */
   useEffect(() => {
        if (selectedStationDepart && selectedStationArrival) {
            fetchLinesByStations(selectedStationDepart, selectedStationArrival, selectedAbnType?.id);
        } else {
            setAvailableLines([]);
            setSelectedLine(null);
            setLineDetails(null);
        }
    }, [selectedStationDepart, selectedStationArrival, selectedLine, selectedAbnType]);

    const fetchLinesByStations = async (departureStationId: number, arrivalStationId: number, subsTypeId = null) => {
        const typeId = subsTypeId || selectedAbnType?.id;

        if (!departureStationId || !arrivalStationId || !typeId) {
            setAvailableLines([]);
            return [];
        }
        try {
            setIsLoadingLines(true);
            const response = await dispatch(getLinesByStations({
                departureStationId,
                arrivalStationId,
                subsTypeId: typeId
            })).unwrap();
            setAvailableLines(response?.data || []);
            return response?.data || [];
        } catch (error) {
            console.error('Error fetching lines:', error);
            toast.error(t("common.errors.unexpected"));
            setAvailableLines([]);
            return [];
        } finally {
            setIsLoadingLines(false);
        }
    };

    const handleLineChange = async (lineId: number) => {
        setSelectedLine(lineId);
        form.setFieldsValue({ id_line: lineId });

        if (!lineId) {
            setLineDetails(null);
            return null;
        }

        try {
            setIsLoadingLineDetails(true);

            const selectedLineData = availableLines.find(line => line.id === lineId);

            if (lineDetails?.id === lineId && lineDetails.stations && lineDetails.stations.length > 0) {
                if (selectedStationDepart && selectedStationArrival) {
                    updateIsReversedBasedOnStations(lineDetails, selectedStationDepart, selectedStationArrival);
                }
                return lineDetails;
            }

            const response = await dispatch(getLineStationsAndRoutes(lineId)).unwrap();

            let lineData = null;

            if (response && response.data) {
                lineData = response.data;
            } else if (response) {
                lineData = response;
            }

            if (lineData) {
                if (selectedLineData) {
                    if (!lineData.name && !lineData[`nom_${currentLang}`]) {
                        lineData.name = selectedLineData.name || selectedLineData[`nom_${currentLang}`];
                        lineData[`nom_${currentLang}`] = selectedLineData[`nom_${currentLang}`] || selectedLineData.name;
                    }
                }

                if (lineData.stations && lineData.stations.length > 0 && selectedStationDepart && selectedStationArrival) {
                    updateIsReversedBasedOnStations(lineData, selectedStationDepart, selectedStationArrival);
                }
                setLineDetails(lineData);
            } else {
                setLineDetails(null);
            }

            return lineData;
        } catch (error) {
            console.error('Error fetching line details:', error);
            toast.error(t("common.errors.unexpected"));
            setLineDetails(null);
            return null;
        } finally {
            setIsLoadingLineDetails(false);
        }
    };

    const handleUpload = (file: File) => {
        const reader = new FileReader();
        reader.addEventListener('load', () => {
            if(reader.result) {
                setImageSrc(reader.result.toString());
                setCropModalVisible(true);
            }
        });
        reader.readAsDataURL(file);
        return false;
    };

      const handleGovernorateChange: any = async (governorateId: number | null) => {
        form.setFieldsValue({ id_delegation: null });
        setSelectedFilterGovernorate(governorateId);
        setIsDelegationsLoading(true);
        setFilteredDelegations([]);
        if (!governorateId) {
            setFilteredDelegations([]);
            setIsDelegationsLoading(false);
            return;
        }
        try {
            const response:any = await dispatch(getDelegationsByGovernorate(governorateId)).unwrap();
            setFilteredDelegations(response.data || []);
        } catch (error) {
            console.error('Error fetching delegations:', error);
            toast.error(t("common.errors.unexpected"));
        } finally {
            setIsDelegationsLoading(false);
        }
    };

    const handleDelegationChange: any = async (delegationId: any) => {
        setSelectedDelegation(delegationId);
        fetchStations(selectedAbnType?.id, delegationId);
    };

    const fetchStations = async (subsTypeId = null, delegationId = null) => {
        const typeId = subsTypeId || selectedAbnType?.id;

        console.log(typeId);


        if (!typeId) {
            return;
        }

        if(!selectedAbnType?.is_student){
             delegationId = null;
        }

        try {
            setIsLoadingStations(true);
            let stationsResponse;

            const params = {
                subsTypeId: typeId,
                delegationId: delegationId || null
            };

            stationsResponse = await dispatch(getStationsBySubscriptionType(params)).unwrap();

            setStations(stationsResponse?.data || []);

            return stationsResponse?.data || [];
        } catch (error) {
            console.error('Error fetching stations:', error);
            toast.error(t("common.errors.unexpected"));
            return [];
        } finally {
            setIsLoadingStations(false);
        }
    };

    const fetchConnectedStations = async (departureStationId: number, subsTypeId = null, delegationId = null) => {
        const typeId = subsTypeId || selectedAbnType?.id;

        if (!departureStationId || !typeId) {
            setConnectedStations([]);
            return [];
        }

        if(!selectedAbnType?.is_student){
            delegationId = null;
       }

        try {
            setIsLoadingConnectedStations(true);

            const response = await dispatch(getStationsForTrip({
                stationId: departureStationId,
                subsTypeId: typeId,
                delegationId: delegationId || null
            })).unwrap();

            setConnectedStations(response?.data || []);
            return response?.data || [];
        } catch (error) {
            console.error('Error fetching connected stations:', error);
            toast.error(t("common.errors.unexpected"));
            setConnectedStations([]);
            return [];
        } finally {
            setIsLoadingConnectedStations(false);
        }
    };

     const updateIsReversedBasedOnStations = (lineData: any, departureStationId: number, arrivalStationId: number) => {
        if (!lineData || !lineData.stations || !departureStationId || !arrivalStationId) {
            return;
        }

        const departureStationIndex = lineData.stations.findIndex((station: any) => station.id === departureStationId);
        const arrivalStationIndex = lineData.stations.findIndex((station: any) => station.id === arrivalStationId);

        if (departureStationIndex !== -1 && arrivalStationIndex !== -1) {
            const isReversed = departureStationIndex > arrivalStationIndex;
            setIsReversed(isReversed);
            return isReversed;
        }
        return null;
    };

  const handleSearchStudentOrClient = async () => {

      const toastId = toast.loading(t("messages.loading"), {
          position: 'top-center',
      });
      try {
          const values = await form.validateFields(['identity_number_search', 'dob_search']);
          setLoadingSearchClient(true);

          let payload;

          if (!selectedAbnType?.is_student) {
              if (specialClient === "CIVIL") {
                  payload = {
                      cin: values.identity_number_search,
                      jourNaiss: dayjs(values.dob_search).format('DD'),
                      moisNaiss: dayjs(values.dob_search).format('MM'),
                      anneeNaiss: dayjs(values.dob_search).format('YYYY')
                  };
              } else if (specialClient === "SCOLAIRE") {
                  payload = {
                      identifiant: values.identity_number_search,
                      date_naissance: dayjs(values.dob_search).format('DD/MM/YYYY')
                  };
              } else if (specialClient === "UNIVERSITAIRE") {
                  payload = {
                      id_etud: values.identity_number_search,
                      date_naissance: dayjs(values.dob_search).format('DD/MM/YYYY')
                  };
              }
          } else {
              payload = selectedAbnType?.is_student && !selectedAbnType?.hasCIN ? {
                  identifiant: values.identity_number_search,
                  date_naissance: dayjs(values.dob_search).format('DD/MM/YYYY')
              } : selectedAbnType?.hasCIN && selectedAbnType?.is_student ? {
                  id_etud: values.identity_number_search,
                  date_naissance: dayjs(values.dob_search).format('DD/MM/YYYY')
              } : null;
          }

          let response;

          if (!selectedAbnType?.is_student) {
              if (specialClient === "CIVIL") {
                  response = await dispatch(searchCitoyen(payload));
              } else if (specialClient === "SCOLAIRE") {
                  response = await dispatch(searchStudentWithoutCIN(payload));
              } else if (specialClient === "UNIVERSITAIRE") {
                  response = await dispatch(searchStudentWithCIN(payload));
              }
          } else {
              response = !selectedAbnType?.hasCIN && selectedAbnType?.is_student
                  ? await dispatch(searchStudentWithoutCIN(payload))
                  : selectedAbnType?.hasCIN && selectedAbnType?.is_student
                  ? await dispatch(searchStudentWithCIN(payload))
                  : null;
          }



          if (response?.payload?.status === "success") {
                const data = response.payload.data;

                setSelectedClient(data);

                let establishment = null;

                if (selectedAbnType?.is_student ||
                    (!selectedAbnType?.is_student && (specialClient === "SCOLAIRE" || specialClient === "UNIVERSITAIRE"))) {

                    establishment = establishments.find((est: any) => est.cnss_etab == data.cnss_etab);
                    setSelectedEstablishment(establishment);

                    const degree = schoolDegrees.find((deg: any) => deg[`nom_fr`] === data.niveau);

                    if (degree) {
                        form.setFieldsValue({
                            id_degree: degree.id,
                        });
                        setSelectedClient({...data});
                    } else if (data.niveau) {
                        dispatch(storeSchoolDegree({
                            nom_fr: data.niveau,
                            nom_en: data.niveau,
                            nom_ar: data.niveau,
                            id_type_establishment: establishment?.id_type_establishment || null,
                            age_max: 18
                        })).unwrap().then(async (response: any) => {
                            try {
                                await dispatch(getSchoolDegreeAll()).unwrap();

                                form.setFieldsValue({
                                    id_degree: response.data.id,
                                });

                                setSelectedClient({...data});
                            } catch (error: any) {
                                console.error('Error refreshing school degrees:', error);
                            }
                        });
                    }
                }

              let dobValue;
              if (data.date_naissance) {
                  dobValue = dayjs(data.date_naissance, 'DD/MM/YYYY');
              } else if (data.jourNaiss && data.moisNaiss && data.anneeNaiss) {
                  dobValue = dayjs(`${data.jourNaiss}/${data.moisNaiss}/${data.anneeNaiss}`, 'DD/MM/YYYY');
              } else {
                  dobValue = null;
              }

              form.setFieldsValue({
                  firstname: data.nom_insc || data.nomFr,
                  lastname: data.prenom_insc || data.prenomFr,
                  dob: dobValue,
                  identity_number: values.identity_number_search,
                  id_establishment: establishment?.id || null
              });

              toast.update(toastId, {
                  render: t("messages.success"),
                  type: "success",
                  isLoading: false,
                  autoClose: 3000
              });

              if (selectedAbnType?.id) {
                  await fetchStations(selectedAbnType.id);
              }
            } else {
                setSelectedClient(null);
                toast.update(toastId, {
                    render: t("messages.clientNotFound"),
                    type: "error",
                    isLoading: false,
                    autoClose: 3000
                });
            }
      } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.clientNotFound"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
      } finally {
          setLoadingSearchClient(false);
      }
  };

     const handlePayment = (record: any) => {
        setAbnRecord(record);

        const now = dayjs();
        const relatedPeriods = (salesPeriods || []).filter(
        (period:any) => period.id_abn_type === record.subs_type.id
        );
        const active = relatedPeriods.find(
        (period:any) =>
            dayjs(period.date_start).isBefore(now) &&
            dayjs(period.date_end).isAfter(now)
        );
    
        setActivePeriod(active || null);
        
        const isRenewalRecord = record.id_parent;
        setIsRenewal(!!isRenewalRecord);

        setPaymentModal(true);
    };

        const handleViewCard:any = (record:any) => {
        setAbnRecord(record);
        setIsCardModalVisible(true);
    };

    const handleOpenReceipt:any = (record:any) => {
        setAbnRecord(record);
        setIsReceiptModalVisible(true);
    };

  /*|--------------------------------------------------------------------------
  |  - ADD OR UPDATE SUBS
  |-------------------------------------------------------------------------- */
  const handleFormSubmit = async (values: any) => {
      const toastId = toast.loading(t("messages.loading"), {
          position: 'top-center',
      });

      const photoValue = values.photo;

      // Prepare client data
      const clientData = {
          firstname: values.firstname,
          lastname: values.lastname,
          identity_number: values.identity_number,
          phone: values.phone,
          address: values.address,
          dob: values.dob ? dayjs(values.dob).format('YYYY-MM-DD') : null,
          id_governorate: values.id_governorate || null,
          id_delegation: values.id_delegation || null,
          id_degree: values.id_degree || null,
          id_establishment: values?.id_establishment || null,

          // For subscribers to authenticate
          email: values.email,
          password: values.password || 'client'
      };

      const subscriptionData = {
          is_stagiaire: values?.is_stagiaire === true ? 1 : 0,
          stage_date_start: values.stage_date_start ? dayjs(values.stage_date_start).format('YYYY-MM-DD') : null,
          stage_date_end: values.stage_date_end ? dayjs(values.stage_date_end).format('YYYY-MM-DD') : null,
          id_subs_type: values.id_subs_type || selectedAbnType?.id,
          id_periodicity: values.id_periodicity,
          id_station_start: isReversed === true ? selectedStationArrival : selectedStationDepart,
          id_station_end: isReversed === true ? selectedStationDepart : selectedStationArrival,
          id_line: selectedLine,
          id_sale_period: activePeriod?.id || null,
          is_reversed: isReversed === true ? 1 : 0,
          photo: photoValue || croppedImage,
          //is_social_affair: isSocialAffair ? 1 : 0,
          status: values.status || 'NOTPAYED',
          hasVacances: values.hasVacances === true ? 1 : 0,
          id_trip: values.id_trip || null,
          rest_days: values.rest_days || [],
          subs_number: values.subs_number,
          special_client: !selectedAbnType?.is_student ? values.special_client : null
      };

      let payload;


      payload = {
          ...subscriptionData,
          ...clientData
      };


      try {
          await dispatch(storeClientWithSubscription(payload)).unwrap();
          toast.update(toastId, {
              render: t("messages.success"),
              type: "success",
              isLoading: false,
              autoClose: 3000
          });

          await dispatch(getLastSubscriptionByClient()).unwrap();
          handleReset()
      } catch (error: any) {
          const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
              name: field,
              errors: [error.errors[field][0]],
          }));
          form.setFields(fieldErrors);
          toast.update(toastId, {
              render: error.message,
              type: "error",
              isLoading: false,
              autoClose: 3000
          });
      }
  };

  const confirmSubmit = (values: any) => {
      const modal = Modal.confirm({
          title: t("manage_newSubs.confirmAction"),
          content: t("manage_newSubs.confirmAdd"),
          okText: t("common.yes"),
          cancelText: t("common.no"),
          onOk: async () => {
              modal.destroy();
              await handleFormSubmit(values);
          },
          centered: true,
      });
  };


  /*-----------------------------------
  * RESET
  -----------------------------------*/
  const handleReset = () => {
    setIsAddModalVisible(false);
    setSelectedAbnType(null);
    form.resetFields();
    setSelectedClient(null);
    setSelectedStationDepart(null);
    setSelectedStationArrival(null);
    setSelectedLine(null);
    setCroppedImage(null);
    setSelectedAbnType(null);
    setStations([]);
    setConnectedStations([]);
    setAvailableLines([]);
    setLineDetails(null);
    setIsReversed(false);
    setFilteredDelegations([]);
    setSelectedFilterGovernorate(null);
    setAvailableLines([]);
    setIsStagiaire(false);
    setSelectedDelegation(null);
    setPaymentModal(false);
    setSpecialClient(null);
    setActivePeriod(null);
  };

  return (
    <>
    <Breadcrumb className="mb-5" items={breadcrumbItems} />
    {loadingFetch ? (
    <Loading />
    ) : (

        <>
            <div className="mb-8">
                <Row gutter={[20, 20]}>
                {/* List of subscriptions */}
                <Col span={24}>
                    <Row gutter={[20, 20]}>
                    {subsTypes.map((subscription: any, index: number) => {
                        const subscriptionStatus = getSubscriptionStatus(
                        subscription.id,
                        salesPeriods || []
                        );
                        const isAvailable = subscriptionStatus.status === "available";
                        const statusColor = isAvailable ? "var(--secondary-color)" : "var(--third-color)";

                        return (
                        <Col key={index} xl={8} lg={8} md={12} sm={12} xs={24}>
                            <Badge.Ribbon
                            text={(() => {
                                if (isAvailable) {
                                return t("subscriptions.available");
                                } else if (subscriptionStatus.status === "upcoming") {
                                return `${t("subscriptions.upcoming")} (${t(
                                    "subscriptions.in"
                                )} ${subscriptionStatus.daysRemaining} ${t(
                                    "subscriptions.days"
                                )})`;
                                } else {
                                return t("subscriptions.not_available");
                                }
                            })()}
                            color={statusColor}
                            className="font-medium"
                            >
                            <div
                                className="group bg-white border border-gray-200 rounded-xl cursor-pointer transition-all duration-300 hover:border-blue-300 hover:shadow-lg hover:shadow-blue-100/50 overflow-hidden"
                                onClick={() => {
                                handleClickCard(subscription);
                                }}
                            >
                                <div className="relative overflow-hidden">
                                <img
                                    className="w-full object-cover transition-transform duration-300 group-hover:scale-105"
                                    style={{ height: "220px" }}
                                    alt={subscription[`nom_${currentLang}`]}
                                    src={getTypeImage(subscription.id)}
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                </div>
                                <div className="p-5">
                                <div className="text-center space-y-3">
                                    <Typography.Title level={4} className="mb-0 text-gray-800 group-hover:text-blue-700 transition-colors duration-200">
                                    {subscription[`nom_${currentLang}`]}
                                    </Typography.Title>
                                    {isAvailable ? (
                                        <div className="inline-flex items-center px-3 py-2 bg-green-50 text-green-700 rounded-full text-sm font-medium border border-green-200">
                                            <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                                            {t("subscriptions.clickToSubscribe")}
                                        </div>
                                    ): (
                                        <div className="inline-flex items-center px-3 py-2 bg-red-50 text-red-700 rounded-full text-sm font-medium border border-red-200">
                                            <span className="inline-block w-2 h-2 rounded-full bg-red-500 mr-2"></span>
                                            {t("subscriptions.not_available")}
                                        </div>
                                    )}
                                </div>
                                </div>
                            </div>
                            </Badge.Ribbon>
                        </Col>
                        );
                    })}
                    </Row>
                </Col>
                </Row>
            </div>
             {/* Last Subscription Section */}
            {lastSubscription && (
            <div className="mt-6">
                <Row gutter={[16, 16]}>
                <Col span={24}>
                    <div className="flex items-center">
                    <div className="text-sm text-gray-500">
                        {t("subscriptions.lastSubscription")}
                    </div>
                    <div className=" flex-grow bg-gradient-to-r from-gray-500/30 to-transparent rounded-full"></div>
                    </div>
                </Col>
                <Col span={24}>
                    <Spin spinning={lastSubscriptionLoading}>
                    <div className="transport-card relative shadow w-full group transition-all duration-300">
                        <div className="relative rounded overflow-hidden border border-gray-200/50 bg-white backdrop-blur-sm">
                            <div className="absolute inset-0 overflow-hidden">
                            <div className="absolute inset-0 bg-gradient-to-br from-slate-50/80 via-white/40 to-blue-50/60"></div>
                            <div
                            className="absolute top-0 left-0 w-full h-full opacity-[0.03] transition-opacity duration-700 group-hover:opacity-[0.06]"
                            style={{
                                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234f46e5' fill-opacity='0.15'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3Ccircle cx='50' cy='10' r='1'/%3E%3Ccircle cx='10' cy='50' r='1'/%3E%3Ccircle cx='50' cy='50' r='1'/%3E%3Ccircle cx='30' cy='10' r='0.5'/%3E%3Ccircle cx='30' cy='50' r='0.5'/%3E%3Ccircle cx='10' cy='30' r='0.5'/%3E%3Ccircle cx='50' cy='30' r='0.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                                zIndex: 0
                            }}
                            ></div>
                            <div className="absolute inset-0 bg-gradient-to-r from-primary-color/3 via-transparent to-secondary-color/3 transition-opacity duration-500 group-hover:from-primary-color/5 group-hover:to-secondary-color/5"></div>
                            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-color via-secondary-color to-primary-color"></div>
                        </div>

                        <div className="relative z-10 px-4 py-3 flex justify-between items-center border-b border-gray-100/80 bg-gradient-to-r from-white/90 to-gray-50/50 backdrop-blur-sm">
                            <div className="flex items-center space-x-3">
                            <div className="relative">
                                <div className="bg-gradient-to-br from-primary-color to-primary-color/80 p-1.5 rounded-lg shadow-md ring-1 ring-white/50">
                                <img src={assets.logo} alt="SRTGN" className="h-6 w-6 object-contain  " />
                                </div>
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                            </div>
                            <div>
                                <h3 className="font-bold mx-2 uppercase tracking-wider">SRTGN</h3>
                                <p className="text-xs text-gray-500 mx-2 font-medium">{t("subscriptions.transportCard")}</p>
                            </div>
                            </div>
                            <div className="text-right space-y-2">
                            <div className="text-sm font-bold tracking-wide bg-gradient-to-r from-primary-color/10 to-secondary-color/10 text-gray-800 rounded-lg px-3 py-1.5 border border-primary-color/20 backdrop-blur-sm">
                                {lastSubscription.subs_type?.[`nom_${currentLang}`]}
                            </div>
                            <div className="text-xs font-medium bg-gradient-to-r from-gray-50 to-white text-gray-700 rounded-md px-2 py-1 border border-gray-200/50 inline-block">
                                {lastSubscription.periodicity?.[`nom_${currentLang}`]}
                            </div>
                            </div>
                        </div>

                        <div className="relative z-10 p-4">
                            <div className="flex flex-col md:flex-row gap-4">
                            <div className="w-full md:w-1/6 flex flex-col items-center mb-4 md:mb-0">
                                <div className="relative group">
                                <div className="w-24 h-24 sm:w-28 sm:h-28 rounded overflow-hidden border border-gray-300 shadow relative">
                                    {lastSubscription.photo ? (
                                        <img
                                            src={formatImagePath(lastSubscription.photo)}
                                            alt="Subscription Photo"
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                       <div className=" ">
                                         <UserIcon size={24} className="text-white" />
                                       </div>
                                    )}
                                </div>
                                </div>
                                <div className="mt-3 text-center">
                                <div
                                    className={`inline-flex items-center text-xs px-3 py-1.5 rounded-full font-semibold border-2 transition-all duration-300 ${
                                    lastSubscription.status === 'PAYED'
                                        ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-400 hover:shadow-green-200'
                                        : lastSubscription.status === 'NOTPAYED'
                                        ? 'bg-gradient-to-r from-yellow-50 to-amber-50 text-yellow-700 border-yellow-400 hover:shadow-yellow-200'
                                        : 'bg-gradient-to-r from-red-50 to-rose-50 text-red-700 border-red-400 hover:shadow-red-200'
                                    }`}
                                >
                                    <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                                    lastSubscription.status === 'PAYED' ? 'bg-green-500' :
                                    lastSubscription.status === 'NOTPAYED' ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}></span>
                                    {lastSubscription.status === 'PAYED'
                                    ? t("manage_newSubs.paymentOptions.payed")
                                    : lastSubscription.status === 'NOTPAYED'
                                    ? t("manage_newSubs.paymentOptions.notPayed")
                                    : t("manage_newSubs.paymentOptions.canceled")}
                                </div>
                                </div>
                            </div>

                            <div className="w-full md:w-5/6">
                                <div className="mb-4 pb-3 border-b border-gray-100/50">
                                <h2 className="text-base sm:text-lg font-bold text-gray-800 mb-2 flex flex-wrap items-center gap-2">
                                    <span className="bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                                    {lastSubscription.client?.firstname} {lastSubscription.client?.lastname}
                                    </span>
                                    <span className="text-xs bg-gradient-to-r from-gray-100 to-gray-50 text-gray-600 px-2 py-1 rounded border border-gray-200/50 font-medium">
                                    <IdCardIcon size={16} className="inline mb-1 mx-1" />
                                    {t("manage_newSubs.labels.cin", "CIN")}: {lastSubscription.client?.identity_number || 'N/A'}
                                    </span>
                                </h2>
                                </div>

                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
                                <div className="bg-gradient-to-br from-blue-50/50 via-white to-indigo-50/30 rounded p-3 border border-blue-100/50 transition-all duration-300 group">
                                    <h3 className="text-xs font-bold text-primary-color mb-2 flex items-center">
                                    <span className="inline-block w-1.5 h-1.5 bg-primary-color rounded-full mx-1.5 animate-pulse"></span>
                                    {t("manage_newSubs.labels.route")}
                                    </h3>
                                    <div className="flex items-center mb-2">
                                    <div className="w-3 h-3 rounded-full bg-gradient-to-r from-primary-color to-primary-color/80 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                    </div>
                                    <div className="mx-1.5 flex-1 border-t border-dashed border-primary-color/40 relative">
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-0.5 bg-primary-color/60 rounded-full"></div>
                                    </div>
                                    <div className="w-3 h-3 rounded-full bg-gradient-to-r from-secondary-color to-secondary-color/80 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                    </div>
                                    </div>
                                    <div className="flex justify-between">
                                    <div className="text-xs font-semibold text-primary-color truncate max-w-[45%] bg-primary-color/5 px-1.5 py-0.5 rounded" title={lastSubscription?.trip?.station_start?.[`nom_${currentLang}`]}>
                                        {lastSubscription?.trip?.station_start?.[`nom_${currentLang}`]}
                                    </div>
                                    <div className="text-xs font-semibold text-secondary-color truncate max-w-[45%] bg-secondary-color/5 px-1.5 py-0.5 rounded" title={lastSubscription?.trip?.station_end?.[`nom_${currentLang}`]}>
                                        {lastSubscription?.trip?.station_end?.[`nom_${currentLang}`]}
                                    </div>
                                    </div>
                                </div>

                                <div className="bg-gradient-to-br from-purple-50/50 via-white to-pink-50/30 rounded p-3 border border-purple-100/50  transition-all duration-300 group">
                                    <h3 className="text-xs font-bold text-primary-color mb-2 flex items-center">
                                    <span className="inline-block w-1.5 h-1.5 bg-primary-color rounded mx-1.5 animate-pulse"></span>
                                    {t("manage_newSubs.labels.subscriptionType")}
                                    </h3>
                                    <div className="flex items-center mb-2">
                                    <div className="w-3 h-3 rounded-full bg-gradient-to-r from-primary-color to-primary-color/80 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                    </div>
                                    <div className="mx-1.5 flex-1 border-t border-dashed border-primary-color/40 relative">
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-0.5 bg-primary-color/60 rounded-full"></div>
                                    </div>
                                    <div className="w-3 h-3 rounded-full bg-gradient-to-r from-secondary-color to-secondary-color/80 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                    </div>
                                    </div>
                                    <p className="text-xs font-semibold text-gray-700 bg-gradient-to-r from-gray-50 to-white px-2 py-1 rounded border border-gray-100 truncate" title={lastSubscription.subs_type?.[`nom_${currentLang}`]}>
                                    {lastSubscription.subs_type?.[`nom_${currentLang}`]}
                                    </p>
                                </div>

                                <div className="bg-gradient-to-br from-green-50/50 via-white to-emerald-50/30 rounded p-3 border border-green-100/50  transition-all duration-300 group">
                                    <h3 className="text-xs font-bold text-primary-color mb-2 flex items-center">
                                    <span className="inline-block w-1.5 h-1.5 bg-primary-color rounded mx-1.5 animate-pulse"></span>
                                    {t("manage_newSubs.labels.periodicity")}
                                    </h3>
                                    <div className="flex items-center mb-2">
                                    <div className="w-3 h-3 rounded-full bg-gradient-to-r from-primary-color to-primary-color/80 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                    </div>
                                    <div className="mx-1.5 flex-1 border-t border-dashed border-primary-color/40 relative">
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-0.5 bg-primary-color/60 rounded-full"></div>
                                    </div>
                                    <div className="w-3 h-3 rounded-full bg-gradient-to-r from-secondary-color to-secondary-color/80 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                    </div>
                                    </div>
                                    <p className="text-xs font-semibold text-gray-700 bg-gradient-to-r from-gray-50 to-white px-2 py-1 rounded border border-gray-100 truncate" title={lastSubscription.periodicity?.[`nom_${currentLang}`]}>
                                    {lastSubscription.periodicity?.[`nom_${currentLang}`]}
                                    </p>
                                </div>
                                </div>

                                {/* Affichage des informations de période et de montant pour les abonnements payés */}
                                {lastSubscription.status === 'PAYED' && (
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mt-3">
                                    <div className="bg-gradient-to-br from-emerald-50/70 via-green-50/50 to-teal-50/30 rounded-lg p-3 border border-emerald-200/50  transition-all duration-300 group">
                                    <h3 className="text-xs font-bold text-emerald-700 mb-2 flex items-center">
                                        <span className="inline-block w-1.5 h-1.5 bg-emerald-500 rounded-full mx-1.5 animate-pulse"></span>
                                        {t("manage_newSubs.labels.startDate", "Date de début")}
                                    </h3>
                                    <div className="flex items-center mb-2">
                                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-emerald-500 to-emerald-600 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                        </div>
                                        <div className="mx-1.5 flex-1 border-t border-dashed border-emerald-400/50 relative">
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-0.5 bg-emerald-500/60 rounded-full"></div>
                                        </div>
                                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-emerald-600 to-emerald-700 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                        </div>
                                    </div>
                                    <p className="text-xs font-bold text-emerald-800 bg-gradient-to-r from-white/80 to-emerald-50/50 px-2 py-1 rounded border border-emerald-100">
                                        {lastSubscription.start_date ? dayjs(lastSubscription.start_date).format('DD/MM/YYYY') : '-'}
                                    </p>
                                    </div>

                                    <div className="bg-gradient-to-br from-amber-50/70 via-yellow-50/50 to-orange-50/30 rounded-lg p-3 border border-amber-200/50  transition-all duration-300 group">
                                    <h3 className="text-xs font-bold text-amber-700 mb-2 flex items-center">
                                        <span className="inline-block w-1.5 h-1.5 bg-amber-500 rounded-full mx-1.5 animate-pulse"></span>
                                        {t("manage_newSubs.labels.endDate", "Date d'expiration")}
                                    </h3>
                                    <div className="flex items-center mb-2">
                                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-amber-500 to-amber-600 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                        </div>
                                        <div className="mx-1.5 flex-1 border-t border-dashed border-amber-400/50 relative">
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-0.5 bg-amber-500/60 rounded-full"></div>
                                        </div>
                                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-amber-600 to-amber-700 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                        </div>
                                    </div>
                                    <p className="text-xs font-bold text-amber-800 bg-gradient-to-r from-white/80 to-amber-50/50 px-2 py-1 rounded border border-amber-100">
                                        {lastSubscription.end_date ? dayjs(lastSubscription.end_date).format('DD/MM/YYYY') : '-'}
                                    </p>
                                    </div>

                                    <div className="bg-gradient-to-br from-blue-50/70 via-indigo-50/50 to-purple-50/30 rounded-lg p-3 border border-blue-200/50  transition-all duration-300 group">
                                    <h3 className="text-xs font-bold text-blue-700 mb-2 flex items-center">
                                        <span className="inline-block w-1.5 h-1.5 bg-blue-500 rounded-full mx-1.5 animate-pulse"></span>
                                        {t("manage_newSubs.labels.amount", "Montant payé")}
                                    </h3>
                                    <div className="flex items-center mb-2">
                                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                        </div>
                                        <div className="mx-1.5 flex-1 border-t border-dashed border-blue-400/50 relative">
                                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-0.5 bg-blue-500/60 rounded-full"></div>
                                        </div>
                                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-600 to-blue-700 flex items-center justify-center ring-1 ring-white">
                                        <div className="w-1 h-1 rounded-full bg-white"></div>
                                        </div>
                                    </div>
                                    <p className="text-xs font-bold text-blue-800 bg-gradient-to-r from-white/80 to-blue-50/50 px-2 py-1 rounded border border-blue-100">
                                        {lastSubscription.latestTransaction?.amount ? `${lastSubscription.latestTransaction.amount} TND` : '-'}
                                    </p>
                                    </div>
                                </div>
                                )}
                            </div>
                            </div>
                        </div>

                        {/* Card Footer */}
                        <div className="relative z-10 px-4 py-3 bg-gradient-to-r from-gray-50/80 via-white/60 to-gray-50/80 border-t border-gray-100/50 backdrop-blur-sm flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                            <div className="flex items-center space-x-2">
                            <div className="relative">
                                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-red-400 to-red-500 animate-pulse"></div>
                                <div className="absolute inset-0 w-3 h-3 rounded-full bg-red-400 animate-ping opacity-20"></div>
                            </div>
                            <div className="text-xs text-gray-600 font-medium">
                                {t("subscriptions.validityInfo")}
                            </div>
                            </div>
                            {lastSubscription.status === 'NOTPAYED' ? (
                                <button
                                    className="group w-full sm:w-auto bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500 hover:from-emerald-600 hover:via-green-600 hover:to-teal-600 text-white px-4 py-2 rounded-lg flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg ring-1 ring-white/50"
                                    onClick={() => handlePayment(lastSubscription)}
                                >
                                    <CreditCardIcon className="w-4 h-4 mr-2" />
                                    <span className="font-semibold text-sm">{t("subscriptions.payNow")}</span>
                                </button>
                                ) : (
                                <div className="flex flex-wrap gap-2 w-full sm:w-auto justify-end">
                                    <button
                                        className="group flex-grow sm:flex-grow-0 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-3 py-2 rounded-md flex items-center justify-center transition-all duration-300  ring-1 ring-white/30"
                                        onClick={() => handlePayment(lastSubscription)}
                                    >
                                        <CreditCardIcon className="w-3.5 h-3.5 mx-1.5 mt-1" />
                                        <span className="font-medium text-xs">{t("manage_newSubs.showSubsDetails")}</span>
                                    </button>

                                    <button
                                        className="group flex-grow sm:flex-grow-0 bg-gradient-to-r from-slate-500 to-gray-600 hover:from-slate-600 hover:to-gray-700 text-white px-3 py-2 rounded-md flex items-center justify-center transition-all duration-300  ring-1 ring-white/30"
                                        onClick={() => handleViewCard(lastSubscription)}
                                    >
                                        <IdCardIcon className="w-3.5 h-3.5 mx-1.5  mt-1" />
                                        <span className="font-medium text-xs">{t("manage_newSubs.showCard")}</span>
                                    </button>

                                    <button
                                        className="group flex-grow sm:flex-grow-0 bg-gradient-to-r from-rose-500 to-red-600 hover:from-rose-600 hover:to-red-700 text-white px-3 py-2 rounded-md flex items-center justify-center transition-all duration-300  ring-1 ring-white/30"
                                        onClick={() => handleOpenReceipt(lastSubscription)}
                                    >
                                        <PrinterIcon className="w-3.5 h-3.5 mx-1.5  mt-1" />
                                        <span className="font-medium text-xs">{t("manage_newSubs.openReceipt")}</span>
                                    </button>
                            </div>
                                )
                            }
                        </div>
                        </div>
                    </div>
                    </Spin>
                </Col>
                </Row>
            </div>
            )}           

        </>
    )}

    
    {/* -------------------------------------------------------------
    --- MODAL TO ADD NEW SUBSCRIPTION
    ----------------------------------------------------------------- */}
    <Modal
        width={1200}
        title={t("manage_newSubs.add")}
        open={isAddModalVisible}
        onCancel={() => handleReset()}
        onOk={() => form.submit()}
        okText={t("manage_newSubs.save")}
        footer={undefined}
      >
        <Spin spinning={loadingFetch}>
            <Form className="form-inputs" form={form} layout="vertical" onFinish={confirmSubmit}>
                <Row gutter={16}>
                    <Col xs={24} sm={24}>
                        {
                                <>

                                    {
                                        selectedAbnType && !selectedAbnType?.is_student && !loadingFetch && (
                                        <FormItem
                                            name="special_client"
                                            label={t("manage_newSubs.labels.specialClient")}
                                            rules={[{ required: true, message: t("manage_newSubs.errors.specialClientRequired") }]}
                                        >
                                            <Select
                                                placeholder={t("manage_newSubs.placeholders.specialClient")}
                                                onChange={(value) => {
                                                    setSelectedClient(null);
                                                    setSpecialClient(value);
                                                }}
                                            >
                                                <Select.Option value={"CIVIL"}>{t("manage_newSubs.labels.civil")}</Select.Option>
                                                <Select.Option value={"UNIVERSITAIRE"}>{t("manage_newSubs.labels.universitaire")}</Select.Option>
                                                <Select.Option value={"SCOLAIRE"}>{t("manage_newSubs.labels.scolaire")}</Select.Option>
                                            </Select>
                                        </FormItem>
                                    )
                                    }

                                    {/* ######### --- SCOLAIRE / CIVIL / UNIV --- #########  */}
                                    {
                                        selectedAbnType && !loadingFetch && (
                                            <>
                                                <div className="bg-gray-50 p-5 rounded-md border border-gray-200 mb-6">
                                                    <h3 className="text-gray-700 font-medium mb-3 flex items-center">
                                                        <SearchIcon className="w-4 h-4 mr-2" />
                                                      {
                                                        (selectedAbnType?.is_student && !selectedAbnType?.hasCIN)
                                                        ? t("manage_newSubs.searchStudent")
                                                        : (selectedAbnType?.is_student && selectedAbnType?.hasCIN)
                                                        ? t("manage_newSubs.searchStudentCIN")
                                                        : t("manage_newSubs.searchClient")
                                                      }
                                                    </h3>
                                                    <Row className="items-end w-full" gutter={[16, 16]}>
                                                        <Col xs={24} lg={10}>
                                                            <Form.Item
                                                                label={
                                                                    <span className="text-gray-700">
                                                                        {(selectedAbnType?.hasCIN && specialClient != "SCOLAIRE")
                                                                        ? t("manage_newSubs.labels.cin")
                                                                        : t("manage_newSubs.labels.matriculate")
                                                                        }
                                                                    </span>
                                                                }
                                                                name="identity_number_search"
                                                                rules={[{
                                                                    required: true,
                                                                    message: selectedAbnType?.hasCIN ? t("manage_newSubs.errors.cinRequired") : t("manage_newSubs.errors.matriculateRequired")
                                                                }]}
                                                                className="mb-0"
                                                            >
                                                                <Input
                                                                    placeholder={selectedAbnType?.hasCIN ? t("manage_newSubs.placeholders.cin") : t("manage_newSubs.placeholders.matriculate")}
                                                                    className="border-gray-300 focus:border-blue-500"
                                                                />
                                                            </Form.Item>
                                                        </Col>

                                                        <Col xs={24} lg={10}>
                                                            <Form.Item
                                                                label={
                                                                  <span className="text-gray-700">
                                                                      {t("manage_newSubs.labels.clientDob")}
                                                                  </span>
                                                                }
                                                                name="dob_search"
                                                                rules={[{
                                                                    required: true,
                                                                    message: t("manage_newSubs.errors.clientDobRequired")
                                                                }]}
                                                                className="mb-0"
                                                            >
                                                                <DatePicker
                                                                    className="w-full border-gray-300 focus:border-blue-500"
                                                                    format="YYYY-MM-DD"
                                                                    placeholder={t("manage_newSubs.placeholders.clientDob")}
                                                              />
                                                            </Form.Item>
                                                        </Col>

                                                        <Col xs={24} lg={4}>
                                                            <Button
                                                                loading={loadingSearchClient}
                                                                className="w-full flex items-center justify-center"
                                                                onClick={() => handleSearchStudentOrClient()}
                                                            >
                                                                <SearchIcon className="w-4 h-4 mr-2" />
                                                                {t("manage_newSubs.search")}
                                                            </Button>
                                                        </Col>
                                                    </Row>
                                                </div>
                                            </>
                                        )
                                    }


                                    {/**
                                     * ---------------------------------------------------------------
                                     *  Client form
                                     * ---------------------------------------------------------------
                                     */}


                                    {selectedClient && selectedAbnType && !loadingFetch && (
                                    <>
                                        <div className="bg-white rounded-xl p-5 mb-6 border border-gray-100">
                                            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                                <div className="p-2 rounded-lg bg-blue-50 text-gray-600 mr-3">
                                                    <UserIcon className="w-5 h-5" />
                                                </div>
                                                {t("manage_newSubs.clientInfo")}
                                            </h3>
                                            <Row gutter={16}>
                                                <Col span={12}>
                                                    <div className="mb-4">
                                                        <div className="text-sm font-medium text-gray-500 mb-1">
                                                            {t("manage_users.labels.lastname")}
                                                        </div>
                                                        <div className="text-base font-semibold text-gray-800 p-2 bg-gray-50 rounded-lg">
                                                            {form.getFieldValue('lastname') || selectedClient?.lastname || '-'}
                                                        </div>
                                                        <Form.Item name="lastname" hidden={true}
                                                            rules={[{ required: true, message: t("manage_users.errors.lastnameRequired") }]}
                                                        />
                                                    </div>
                                                </Col>
                                                <Col span={12}>
                                                    <div className="mb-4">
                                                        <div className="text-sm font-medium text-gray-500 mb-1">
                                                            {t("manage_users.labels.firstname")}
                                                        </div>
                                                        <div className="text-base font-semibold text-gray-800 p-2 bg-gray-50 rounded-lg">
                                                            {form.getFieldValue('firstname') || selectedClient?.firstname || '-'}
                                                        </div>
                                                        <Form.Item name="firstname" hidden={true}
                                                            rules={[{ required: true, message: t("manage_users.errors.firstnameRequired") }]}
                                                        />
                                                    </div>
                                                </Col>
                                            </Row>

                                            <Row gutter={16}>
                                                <Col span={12}>
                                                    <div className="mb-4">
                                                        <div className="text-sm font-medium text-gray-500 mb-1">
                                                            {selectedAbnType && !selectedAbnType?.hasCIN && selectedAbnType?.is_student ||
                                                            specialClient === "SCOLAIRE" || specialClient === "UNIVERSITAIRE"
                                                            ? t("manage_users.labels.matricule")
                                                            : t("manage_users.labels.cin")}
                                                        </div>
                                                        <div className="text-base font-semibold text-gray-800 p-2 bg-gray-50 rounded-lg">
                                                            {form.getFieldValue('identity_number') || selectedClient?.identity_number || '-'}
                                                        </div>
                                                        <Form.Item name="identity_number" hidden={true}
                                                            rules={[
                                                                { required: true, message: t("manage_users.errors.cinRequired") },
                                                                {
                                                                    pattern: /^[A-Z0-9]+$/,
                                                                    message: t("manage_delegations.errors.cinInvalid")
                                                                },
                                                            ]}
                                                        />
                                                    </div>
                                                </Col>
                                                <Col xs={24} sm={12}>
                                                    <div className="mb-4">
                                                        <div className="text-sm font-medium text-gray-500 mb-1">
                                                            {t("manage_users.labels.dob")}
                                                        </div>
                                                        <div className="text-base font-semibold text-gray-800 p-2 bg-gray-50 rounded-lg">
                                                            {form.getFieldValue('dob') ? dayjs(form.getFieldValue('dob')).format('YYYY-MM-DD') :
                                                            selectedClient?.dob ? dayjs(selectedClient.dob).format('YYYY-MM-DD') : '-'}
                                                        </div>
                                                        <Form.Item name="dob" hidden={true}
                                                            rules={[{ required: true, message: t("manage_users.errors.dobRequired") }]}
                                                        />
                                                    </div>
                                                </Col>
                                            </Row>

                                            {
                                            selectedAbnType && (selectedAbnType?.is_student || specialClient === "SCOLAIRE" || specialClient === "UNIVERSITAIRE") && !loadingFetch && (
                                                <Row gutter={16}>
                                                    <Col span={12}>
                                                        <div className="mb-4">
                                                            <div className="text-sm font-medium text-gray-500 mb-1">
                                                                {t("manage_users.labels.establishment")}
                                                            </div>
                                                            <div className="text-base font-semibold text-gray-800 p-2 bg-gray-50 rounded-lg">
                                                                {(() => {
                                                                    const establishmentId = form.getFieldValue('id_establishment');
                                                                    const establishment = establishments?.find((est: any) => est.id === establishmentId);
                                                                    return establishment ? establishment[`nom_${currentLang}`] :
                                                                        (selectedEstablishment ? selectedEstablishment[`nom_${currentLang}`] : '-');
                                                                })()}
                                                            </div>
                                                            <Form.Item
                                                                name="id_establishment"
                                                                hidden={true}
                                                                rules={[{ required: true, message: t("manage_users.errors.establishmentRequired") }]}
                                                            />
                                                        </div>
                                                    </Col>
                                                    <Col span={12}>
                                                        <div className="mb-4">
                                                            <div className="text-sm font-medium text-gray-500 mb-1">
                                                                {(selectedAbnType?.hasCIN && selectedAbnType?.is_student) || specialClient === "SCOLAIRE"
                                                                ? t("manage_users.labels.schoolDegree")
                                                                : (!selectedAbnType?.hasCIN && selectedAbnType?.is_student) || specialClient === "UNIVERSITAIRE"
                                                                ? t("manage_users.labels.univDegree")
                                                                : t("manage_users.labels.degree")}
                                                            </div>
                                                            <div className="text-base font-semibold text-gray-800 p-2 bg-gray-50 rounded-lg">
                                                                {(() => {
                                                                    const degreeId = form.getFieldValue('id_degree');
                                                                    const degree = schoolDegrees?.find((deg: any) => deg.id === degreeId);
                                                                    return degree ? degree[`nom_${currentLang}`] : '-';
                                                                })()}
                                                            </div>
                                                            <Form.Item
                                                                name="id_degree"
                                                                hidden={true}
                                                                rules={[{ required: true, message: t("manage_users.errors.schoolDegreeRequired") }]}
                                                            />
                                                        </div>
                                                    </Col>
                                                </Row>

                                            )
                                        }
                                        </div>


                                    </>
                                    )}

                                    {
                                        selectedAbnType && selectedClient && (
                                            <Row gutter={[16, 16]}>
                                                <Col xs={24} sm={12}>
                                                    <Form.Item
                                                        label={t("manage_users.labels.phone")}
                                                        name="phone"
                                                        rules={[
                                                            { required: true, message: t("manage_users.errors.phoneRequired") },
                                                            {
                                                                pattern: /^[0-9]{8}$/,
                                                                message: t("manage_users.errors.phoneInvalid"),
                                                            },
                                                        ]}
                                                    >
                                                        <Input
                                                            placeholder={t("manage_users.client.placeholders.phone")}
                                                        />
                                                    </Form.Item>
                                                </Col>

                                                    <Col span={12}>
                                                    <Form.Item
                                                        label={t("manage_users.labels.address")}
                                                        name="address"
                                                        rules={[{ required: true, message: t("manage_users.errors.addressRequired") }]}
                                                    >
                                                        <Input
                                                            placeholder={t("manage_users.client.placeholders.address")}
                                                        />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                        )
                                    }

                                    <div className="flex justify-center">
                                        {selectedAbnType && selectedClient && (
                                            <Form.Item
                                                label={t("manage_newSubs.labels.isStagiaire")}
                                                name="is_stagiaire"
                                                valuePropName="checked"
                                                className="mb-0"
                                            >
                                                <Switch
                                                    onChange={(checked) => setIsStagiaire(checked)}
                                                />
                                            </Form.Item>
                                        )}
                                    </div>

                                    {isStagiaire && selectedAbnType && !loadingFetch && (
                                        <Row gutter={16} className="mt-4">
                                            <Col span={12}>
                                                <Form.Item
                                                    label={t("manage_newSubs.labels.stageStartDate")}
                                                    name="stage_date_start"
                                                    rules={[{ required: true, message: t("manage_newSubs.errors.stageStartDateRequired") }]}
                                                >
                                                    <DatePicker
                                                        className="w-full"
                                                        format="YYYY-MM-DD"
                                                        placeholder={t("manage_newSubs.placeholders.stageStartDate")}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={12}>
                                                <Form.Item
                                                    label={t("manage_newSubs.labels.stageEndDate")}
                                                    name="stage_date_end"
                                                    rules={[{ required: true, message: t("manage_newSubs.errors.stageEndDateRequired") }]}
                                                >
                                                    <DatePicker
                                                        className="w-full"
                                                        format="YYYY-MM-DD"
                                                        placeholder={t("manage_newSubs.placeholders.stageEndDate")}
                                                    />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    )}

                                    {
                                        selectedClient && selectedAbnType && !loadingFetch && (
                                            <Divider orientation="left">{t("manage_newSubs.subscriptionInfo")}</Divider>
                                        )
                                    }

                                    {
                                        selectedAbnType && selectedClient && !loadingFetch && (
                                            <Row gutter={16}>
                                                <Col xs={24} sm={12}>
                                                    <Form.Item
                                                        name="id_governorate"
                                                        label={t("manage_users.labels.governorate")}
                                                        rules={[{ required: true, message: t("manage_users.errors.governorateRequired") }]}
                                                    >
                                                        <Select
                                                            placeholder={t("manage_users.placeholders.governorate")}
                                                            onChange={handleGovernorateChange}
                                                            options={governorates?.map((gov: any) => ({
                                                                label: gov[`nom_${currentLang}`],
                                                                value: gov.id,
                                                            }))}
                                                        />
                                                    </Form.Item>
                                                </Col>
                                                <Col xs={24} sm={12}>
                                                    <Form.Item
                                                        name="id_delegation"
                                                        label={t("manage_users.labels.delegation")}
                                                        rules={[{ required: true, message: t("manage_users.errors.delegationRequired") }]}
                                                    >
                                                        <Select
                                                            disabled={!selectedFilterGovernorate}
                                                            placeholder={t("manage_users.placeholders.delegation")}
                                                            onChange={handleDelegationChange}
                                                            options={filteredDelegations.map((del: any) => ({
                                                                label: del[`nom_${currentLang}`],
                                                                value: del.id,
                                                            }))}
                                                            notFoundContent={
                                                                isDelegationsLoading ? (
                                                                    <div className="flex items-center justify-center py-2">
                                                                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                                                    </div>
                                                                ) : (
                                                                    <div className="text-center py-2 text-gray-500">
                                                                        {!selectedFilterGovernorate
                                                                            ? t("manage_users.selectGovernorate")
                                                                            : t("common.noData")}
                                                                    </div>
                                                                )
                                                            }
                                                        />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                        )
                                    }


                                    <Row gutter={16}>
                                        {
                                            selectedClient && selectedAbnType && selectedDelegation &&
                                            ((selectedAbnType?.is_student && selectedEstablishment) || !selectedAbnType?.is_student) &&
                                            !loadingFetch && (
                                                <>
                                                    <Col span={12}>
                                                        <Form.Item
                                                            label={t("manage_newSubs.labels.station_depart")}
                                                            name="station_depart"
                                                            rules={[{
                                                                required: true,
                                                                message: t("manage_newSubs.errors.stationDepartRequired")
                                                            }]}
                                                        >
                                                            <Select
                                                                placeholder={t("manage_newSubs.placeholders.station_depart")}
                                                                loading={isLoadingStations}
                                                                notFoundContent={
                                                                    isLoadingStations ? (
                                                                        <div className="flex justify-center py-2">
                                                                            <Spin size="small" />
                                                                        </div>
                                                                    ) : null
                                                                }
                                                                onChange={(value) => {
                                                                    // Reset station_arrival and lines when station_depart changes
                                                                    setSelectedStationDepart(value);
                                                                    setSelectedStationArrival(null);
                                                                    setAvailableLines([]);
                                                                    setSelectedLine(null);
                                                                    setLineDetails(null);

                                                                    // Reset form fields
                                                                    form.setFieldsValue({
                                                                        station_arrival: undefined,
                                                                        id_line: undefined
                                                                    });

                                                                    if (value && selectedAbnType?.id) {
                                                                        fetchConnectedStations(value, selectedAbnType.id, selectedEstablishment?.id_delegation);
                                                                    }
                                                                }}
                                                            >
                                                                {isLoadingStations ? (
                                                                    <Select.Option disabled value="loading">
                                                                        <div className="flex items-center gap-2">
                                                                            <Spin size="small" />
                                                                            <span>{t("common.loading")}</span>
                                                                        </div>
                                                                    </Select.Option>
                                                                ) : stations.length > 0 ? (
                                                                    stations.map((stop:any) => (
                                                                        <Select.Option key={stop.id} value={stop.id}>
                                                                            {stop[`nom_${currentLang}`] || stop.name}
                                                                        </Select.Option>
                                                                    ))
                                                                ) : (
                                                                    <Select.Option disabled value="no-data">{t("common.noData")}</Select.Option>
                                                                )}
                                                            </Select>
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={12}>
                                                        <Form.Item
                                                            label={t("manage_newSubs.labels.station_arrival")}
                                                            name="station_arrival"
                                                            rules={[{
                                                                required: true,
                                                                message: t("manage_newSubs.errors.stationArrivalRequired")
                                                            }]}
                                                        >
                                                            <Select
                                                                    disabled={!selectedStationDepart}
                                                                    placeholder={t("manage_newSubs.placeholders.station_arrival")}
                                                                    onChange={(value) => {
                                                                        setSelectedStationArrival(value);

                                                                        // Reset lines when station_arrival changes
                                                                        setAvailableLines([]);
                                                                        setSelectedLine(null);
                                                                        setLineDetails(null);

                                                                        // Reset line form field
                                                                        form.setFieldsValue({
                                                                            id_line: undefined
                                                                        });

                                                                        // Update isReversed if we have a line and departure station
                                                                        if (lineDetails && selectedStationDepart) {
                                                                            updateIsReversedBasedOnStations(lineDetails, selectedStationDepart, value);
                                                                        }

                                                                        // Fetch lines when station_arrival changes
                                                                        if (value && selectedStationDepart && selectedAbnType?.id) {
                                                                            fetchLinesByStations(selectedStationDepart, value, selectedAbnType.id);
                                                                        }
                                                                    }}
                                                                    loading={isLoadingConnectedStations}
                                                                    notFoundContent={
                                                                        isLoadingConnectedStations ? (
                                                                            <div className="flex justify-center py-2">
                                                                                <Spin size="small" />
                                                                            </div>
                                                                        ) : null
                                                                    }
                                                            >
                                                                {isLoadingConnectedStations ? (
                                                                    <Select.Option disabled value="loading">
                                                                        <div className="flex items-center gap-2">
                                                                            <Spin size="small" />
                                                                            <span>{t("common.loading")}</span>
                                                                        </div>
                                                                    </Select.Option>
                                                                ) : connectedStations.length > 0 ? (
                                                                    connectedStations.map((stop) => (
                                                                        <Select.Option key={stop.id} value={stop.id}>
                                                                            {stop[`nom_${currentLang}`] || stop.name}
                                                                        </Select.Option>
                                                                    ))
                                                                ) : selectedStationDepart ? (
                                                                    <Select.Option disabled value="no-data">{t("common.noData")}</Select.Option>
                                                                ) : (
                                                                    <Select.Option disabled value="select-departure">{t("manage_newSubs.selectDepartureFirst")}</Select.Option>
                                                                )}
                                                            </Select>
                                                        </Form.Item>
                                                    </Col>
                                                </>
                                            )
                                        }
                                    </Row>

                                    {
                                        selectedStationDepart && selectedStationArrival && selectedDelegation && !loadingFetch && (
                                            <Form.Item
                                                label={t("manage_newSubs.labels.line")}
                                                name="id_line"
                                                rules={[{
                                                    required: true,
                                                    message: t("manage_newSubs.errors.lineRequired")
                                                }]}
                                            >
                                                <Select
                                                        placeholder={t("manage_newSubs.placeholders.line")}
                                                        onChange={handleLineChange}
                                                        loading={isLoadingLines}
                                                        notFoundContent={
                                                            isLoadingLines ? (
                                                                <div className="flex justify-center py-2">
                                                                    <Spin size="small" />
                                                                </div>
                                                            ) : null
                                                        }
                                                        value={selectedLine}
                                                >
                                                    {isLoadingLines ? (
                                                        <Select.Option disabled value="loading">
                                                            <div className="flex items-center gap-2">
                                                                <Spin size="small" />
                                                                <span>{t("common.loading")}</span>
                                                            </div>
                                                        </Select.Option>
                                                    ) : availableLines.length > 0 ? (
                                                        availableLines.map((line) => (
                                                            <Select.Option key={line.id} value={line.id}>
                                                                {line[`nom_${currentLang}`] || line.name || t("common.loading")}
                                                            </Select.Option>
                                                        ))
                                                    ) : (
                                                        <Select.Option disabled value="no-data">{t("common.noData")}</Select.Option>
                                                    )}
                                                </Select>
                                            </Form.Item>
                                        )
                                    }
                                    {
                                        selectedLine && !loadingFetch && (
                                            <div className="pb-6 mb-6">
                                                <div className="flex flex-col">
                                                    {isLoadingLineDetails ? (
                                                        <div className="flex justify-center py-12">
                                                            <Spin tip={t("common.loading")} />
                                                        </div>
                                                    ) : lineDetails ? (
                                                        <LineDisplay
                                                            isReversed={isReversed}
                                                            record={lineDetails}
                                                            selectedStationDepart={selectedStationDepart}
                                                            selectedStationArrival={selectedStationArrival}
                                                        />
                                                    ) : (
                                                        <div className="text-center py-4 text-gray-500">
                                                            {t("common.noData")}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )
                                    }

                                    {
                                        selectedClient && !loadingFetch && (
                                            <Divider />
                                        )
                                    }


                                    {selectedLine && !loadingFetch && (
                                        <Row gutter={[16, 16]} className="mt-8 flex flex-wrap justify-center gap-12">
                                            <div>
                                                {selectedAbnType && selectedLine && !loadingFetch && (
                                                    <div className="flex flex-col gap-3 mt-6">
                                                        {/* Photo Preview */}
                                                        <div className="relative group flex w-full max-w-xs">
                                                            <div className="relative flex justify-center w-48 h-48 rounded-xl border-2 border-dashed border-gray-200 bg-gray-50 hover:border-primary-200 transition-all duration-300">
                                                                <div className="absolute -top-3 -right-3 bg-red-700/80 backdrop-blur-md text-white px-4 py-2 rounded-full flex items-center gap-2 z-10 hover:scale-105 transition-transform">
                                                                    <InfoIcon className="w-5 h-5" />
                                                                    <span className="text-sm font-medium">
                                                                        {t("manage_newSubs.backgroundRequirement")}
                                                                    </span>
                                                                </div>

                                                                {croppedImage ? (
                                                                    <img
                                                                        src={croppedImage}
                                                                        alt="Uploaded preview"
                                                                        className="w-full h-full rounded-xl object-cover"
                                                                    />
                                                                ) : (
                                                                    <div className="absolute inset-0 flex flex-col items-center justify-center space-y-3">
                                                                        <div className="p-3 bg-white/80 rounded-full shadow-inner">
                                                                            <UserIcon className="w-10 h-10 text-primary-500/80" />
                                                                        </div>
                                                                        <span className="text-sm text-gray-600 font-medium text-center px-4">
                                                                            {t("manage_newSubs.noPhoto")}
                                                                        </span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                        <Form.Item
                                                            name="photo"
                                                            valuePropName="src"
                                                            className="w-full"
                                                            rules={[{
                                                                required: true,
                                                                message: t("manage_newSubs.errors.photoRequired"),
                                                            }]}
                                                        >
                                                            <input type="hidden" value={croppedImage || ''} />
                                                            <Upload
                                                                beforeUpload={handleUpload}
                                                                accept="image/*"
                                                                showUploadList={false}
                                                                maxCount={1}
                                                            >
                                                                <Button
                                                                    type="dashed"
                                                                    icon={<UploadIcon className="w-3 h-3" />}
                                                                >
                                                                    {t("manage_newSubs.upload")}
                                                                </Button>
                                                            </Upload>
                                                        </Form.Item>
                                                    </div>
                                                )}
                                            </div>

                                            <Divider
                                                type="vertical"
                                                style={{ height: 'auto', width: 2 }}
                                            />

                                            <Col xs={24} sm={12}>
                                                <div className="flex flex-col gap-3">
                                                    <div>
                                                        {
                                                            selectedLine && (selectedAbnType?.is_impersonal || selectedAbnType?.is_conventional) && !loadingFetch && (
                                                                <Form.Item
                                                                    label={t("manage_newSubs.labels.subsNumber")}
                                                                    name="subs_number"
                                                                    rules={[{ required: true, message: t("manage_newSubs.errors.subsNumberRequired") }]}
                                                                >
                                                                    <Input
                                                                        placeholder={t("manage_newSubs.placeholders.subsNumber")}
                                                                        type="number"
                                                                    />
                                                                </Form.Item>
                                                            )
                                                        }
                                                    </div>
                                                    <div>
                                                        {selectedLine && !loadingFetch && (
                                                            <Form.Item
                                                                label={t("manage_newSubs.labels.periodicity")}
                                                                name="id_periodicity"
                                                                rules={[{
                                                                    required: true,
                                                                    message: t("manage_newSubs.errors.periodicityRequired")
                                                                }]}
                                                            >
                                                                <Select
                                                                    placeholder={t("manage_newSubs.placeholders.periodicity")}
                                                                    onChange={(_) => {
                                                                        form.setFieldsValue({ rest_days: [] });
                                                                        setTimeout(() => {
                                                                            form.validateFields(['rest_days']);
                                                                        }, 0);
                                                                    }}
                                                                >
                                                                    {periodicities.map((el:any) => (
                                                                        <Select.Option key={el.id} value={el.id}>
                                                                            {el[`nom_${currentLang}`]}
                                                                        </Select.Option>
                                                                    ))}
                                                                </Select>
                                                            </Form.Item>
                                                        )}
                                                    </div>

                                                    <div>
                                                        {selectedLine && !selectedAbnType?.is_student && !loadingFetch && (
                                                            <Form.Item
                                                                label={t("manage_newSubs.labels.restDays")}
                                                                name="rest_days"
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: t("manage_newSubs.errors.restDaysRequired")
                                                                    },
                                                                    {
                                                                        validator: (_, value) => {
                                                                            const periodicityId = form.getFieldValue('id_periodicity');
                                                                            if (!periodicityId || !value) return Promise.resolve();

                                                                            const selectedPeriodicity = periodicities.find((p: any) => p.id === periodicityId);
                                                                            if (!selectedPeriodicity) return Promise.resolve();

                                                                            const maxDaysPerWeek = selectedPeriodicity.max_days_per_week;
                                                                            setMaxDaysPerWeek(maxDaysPerWeek);
                                                                            if (!maxDaysPerWeek) return Promise.resolve();

                                                                            return value.length <= maxDaysPerWeek
                                                                                ? Promise.resolve()
                                                                                : Promise.reject(
                                                                                    t("manage_newSubs.errors.tooManyRestDays", {
                                                                                        max: maxDaysPerWeek,
                                                                                        periodicity: selectedPeriodicity[`nom_${currentLang}`]
                                                                                    })
                                                                                );
                                                                        }
                                                                    }
                                                                ]}
                                                            >
                                                                <Select
                                                                    maxCount={maxDaysPerWeek}
                                                                    mode="multiple"
                                                                    placeholder={t("manage_newSubs.placeholders.restDays")}
                                                                >
                                                                    {restDaysData.map((el:any) => (
                                                                        <Select.Option key={el.id} value={el.id}>
                                                                            {el.name}
                                                                        </Select.Option>
                                                                    ))}
                                                                </Select>
                                                            </Form.Item>
                                                        )}

                                                        {selectedLine && selectedAbnType?.is_student && !loadingFetch && (
                                                            <Form.Item
                                                                label={t("manage_newSubs.labels.vacation")}
                                                                name="hasVacances"
                                                                rules={[{
                                                                    required: true,
                                                                    message: t("manage_newSubs.errors.vacationRequired")
                                                                }]}
                                                            >
                                                                <Radio.Group>
                                                                    <Radio value={true}>
                                                                        {t("manage_newSubs.options.withVacation")}
                                                                    </Radio>
                                                                    <Radio value={false}>
                                                                        {t("manage_newSubs.options.withoutVacation")}
                                                                    </Radio>
                                                                </Radio.Group>
                                                            </Form.Item>
                                                        )}
                                                    </div>
                                                </div>
                                            </Col>
                                        </Row>
                                    )}
                                </>
                        }
                    </Col>
                </Row>
            </Form>
        </Spin>
    </Modal>

    <CropModal
        setCroppedImage={setCroppedImage}
        form={form}
        imageSrc={imageSrc}
        cropModalVisible={cropModalVisible}
        setCropModalVisible={setCropModalVisible}
    />


     {/* --------------- Payment Modal --------------- */}
        <PaymentModal
            paymentModal={paymentModal}
            handleReset={handleReset}
            abnRecord={abnRecord}
            loading={loadingFetch}
            croppedImage={croppedImage}
            salesPeriod={activePeriod}
            isRenewal={isRenewal}
            onPaymentSuccess={() => {
                try {
                    dispatch(getLastSubscriptionByClient());
                } catch (error) {
                    console.error("Error refreshing data after payment:", error);
                }
            }}
        />

     {/* --------------- Subs Card Print Modal --------------- */}
        <SubsCardPrintModal
            isVisible={isCardModalVisible}
            onClose={() => {setIsCardModalVisible(false)}}
            abnRecord={abnRecord}
            onPrintSuccess={() => {
                actionRef.current?.reload();
            }}
        />


    {/* --------------- Receipt Print Modal --------------- */}
    <SubsReceipt
        isVisible={isReceiptModalVisible}
        onClose={() => {setIsReceiptModalVisible(false)}}
        abnRecord={abnRecord}
        onPrintSuccess={() => {
            actionRef.current?.reload();
        }}
    />
    </>
  );
};

export default CampaignsList;
